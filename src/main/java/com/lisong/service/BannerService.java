package com.lisong.service;

import com.lisong.dto.BannerRequest;
import com.lisong.dto.BannerResponse;
import com.lisong.entity.Banner;

import java.util.List;

/**
 * 轮播图业务逻辑接口
 */
public interface BannerService {
    
    /**
     * 创建轮播图
     * @param request 轮播图请求信息
     * @param createdBy 创建者ID
     * @return 轮播图响应信息
     */
    BannerResponse createBanner(BannerRequest request, Long createdBy);
    
    /**
     * 根据ID获取轮播图
     * @param id 轮播图ID
     * @return 轮播图响应信息
     */
    BannerResponse getBannerById(Long id);
    
    /**
     * 获取所有轮播图
     * @return 轮播图列表
     */
    List<BannerResponse> getAllBanners();
    
    /**
     * 获取启用的轮播图
     * @return 启用的轮播图列表
     */
    List<BannerResponse> getActiveBanners();
    
    /**
     * 获取当前有效的轮播图（在时间范围内且启用）
     * @return 当前有效的轮播图列表
     */
    List<BannerResponse> getCurrentValidBanners();
    
    /**
     * 根据创建者ID获取轮播图
     * @param createdBy 创建者ID
     * @return 轮播图列表
     */
    List<BannerResponse> getBannersByCreatedBy(Long createdBy);
    
    /**
     * 更新轮播图
     * @param id 轮播图ID
     * @param request 轮播图请求信息
     * @return 轮播图响应信息
     */
    BannerResponse updateBanner(Long id, BannerRequest request);
    
    /**
     * 删除轮播图
     * @param id 轮播图ID
     * @return 是否删除成功
     */
    boolean deleteBanner(Long id);
    
    /**
     * 批量删除轮播图
     * @param ids 轮播图ID列表
     * @return 删除的数量
     */
    int deleteBanners(List<Long> ids);
    
    /**
     * 更新轮播图状态
     * @param id 轮播图ID
     * @param isActive 是否启用
     * @return 是否更新成功
     */
    boolean updateBannerStatus(Long id, Boolean isActive);
    
    /**
     * 获取轮播图总数
     * @return 轮播图总数
     */
    long getBannerCount();
    
    /**
     * 获取启用的轮播图总数
     * @return 启用的轮播图总数
     */
    long getActiveBannerCount();
}
