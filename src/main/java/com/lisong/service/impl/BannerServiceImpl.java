package com.lisong.service.impl;

import com.lisong.dto.BannerRequest;
import com.lisong.dto.BannerResponse;
import com.lisong.entity.Banner;
import com.lisong.mapper.BannerMapper;
import com.lisong.service.BannerService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 轮播图业务逻辑实现类
 */
@Service
@Transactional
public class BannerServiceImpl implements BannerService {
    
    @Autowired
    private BannerMapper bannerMapper;
    
    @Override
    public BannerResponse createBanner(BannerRequest request, Long createdBy) {
        if (request == null) {
            throw new IllegalArgumentException("轮播图信息不能为空");
        }
        
        validateBannerRequest(request);
        
        Banner banner = new Banner();
        BeanUtils.copyProperties(request, banner);
        banner.setCreatedBy(createdBy);
        banner.setCreatedAt(LocalDateTime.now());
        banner.setUpdatedAt(LocalDateTime.now());
        
        // 如果没有设置排序顺序，设置为0
        if (banner.getSortOrder() == null) {
            banner.setSortOrder(0);
        }
        
        int result = bannerMapper.insert(banner);
        if (result > 0) {
            return convertToResponse(bannerMapper.findById(banner.getId()));
        }
        throw new RuntimeException("创建轮播图失败");
    }
    
    @Override
    @Transactional(readOnly = true)
    public BannerResponse getBannerById(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("轮播图ID不能为空或小于等于0");
        }
        
        Banner banner = bannerMapper.findById(id);
        if (banner == null) {
            throw new RuntimeException("轮播图不存在");
        }
        
        return convertToResponse(banner);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<BannerResponse> getAllBanners() {
        List<Banner> banners = bannerMapper.findAll();
        return banners.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<BannerResponse> getActiveBanners() {
        List<Banner> banners = bannerMapper.findActivebanners();
        return banners.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<BannerResponse> getCurrentValidBanners() {
        List<Banner> banners = bannerMapper.findCurrentValidBanners();
        return banners.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<BannerResponse> getBannersByCreatedBy(Long createdBy) {
        if (createdBy == null || createdBy <= 0) {
            throw new IllegalArgumentException("创建者ID不能为空或小于等于0");
        }
        
        List<Banner> banners = bannerMapper.findByCreatedBy(createdBy);
        return banners.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    public BannerResponse updateBanner(Long id, BannerRequest request) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("轮播图ID不能为空或小于等于0");
        }
        if (request == null) {
            throw new IllegalArgumentException("轮播图信息不能为空");
        }
        
        validateBannerRequest(request);
        
        Banner existingBanner = bannerMapper.findById(id);
        if (existingBanner == null) {
            throw new RuntimeException("轮播图不存在");
        }
        
        Banner banner = new Banner();
        BeanUtils.copyProperties(request, banner);
        banner.setId(id);
        banner.setCreatedBy(existingBanner.getCreatedBy());
        banner.setCreatedAt(existingBanner.getCreatedAt());
        banner.setUpdatedAt(LocalDateTime.now());
        
        int result = bannerMapper.update(banner);
        if (result > 0) {
            return convertToResponse(bannerMapper.findById(id));
        }
        throw new RuntimeException("更新轮播图失败");
    }
    
    @Override
    public boolean deleteBanner(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("轮播图ID不能为空或小于等于0");
        }
        
        Banner existingBanner = bannerMapper.findById(id);
        if (existingBanner == null) {
            throw new RuntimeException("轮播图不存在");
        }
        
        int result = bannerMapper.deleteById(id);
        return result > 0;
    }
    
    @Override
    public int deleteBanners(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("轮播图ID列表不能为空");
        }
        
        return bannerMapper.deleteByIds(ids);
    }
    
    @Override
    public boolean updateBannerStatus(Long id, Boolean isActive) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("轮播图ID不能为空或小于等于0");
        }
        if (isActive == null) {
            throw new IllegalArgumentException("状态不能为空");
        }
        
        Banner existingBanner = bannerMapper.findById(id);
        if (existingBanner == null) {
            throw new RuntimeException("轮播图不存在");
        }
        
        int result = bannerMapper.updateStatus(id, isActive);
        return result > 0;
    }
    
    @Override
    @Transactional(readOnly = true)
    public long getBannerCount() {
        return bannerMapper.count();
    }
    
    @Override
    @Transactional(readOnly = true)
    public long getActiveBannerCount() {
        return bannerMapper.countActive();
    }
    
    /**
     * 验证轮播图请求参数
     */
    private void validateBannerRequest(BannerRequest request) {
        if (request.getTitle() == null || request.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("轮播图标题不能为空");
        }
        if (request.getImageUrl() == null || request.getImageUrl().trim().isEmpty()) {
            throw new IllegalArgumentException("轮播图图片URL不能为空");
        }
        if (request.getStartTime() != null && request.getEndTime() != null) {
            if (request.getStartTime().isAfter(request.getEndTime())) {
                throw new IllegalArgumentException("开始时间不能晚于结束时间");
            }
        }
    }
    
    /**
     * 将Banner实体转换为BannerResponse
     */
    private BannerResponse convertToResponse(Banner banner) {
        if (banner == null) {
            return null;
        }
        
        BannerResponse response = new BannerResponse();
        BeanUtils.copyProperties(banner, response);
        
        // 设置创建者用户名
        if (banner.getCreator() != null) {
            response.setCreatorUsername(banner.getCreator().getUsername());
        }
        
        return response;
    }
}
