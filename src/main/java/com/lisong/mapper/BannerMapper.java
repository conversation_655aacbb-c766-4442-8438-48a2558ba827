package com.lisong.mapper;

import com.lisong.entity.Banner;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 轮播图数据访问接口
 */
@Mapper
public interface BannerMapper {
    
    /**
     * 插入新轮播图
     * @param banner 轮播图信息
     * @return 影响行数
     */
    int insert(Banner banner);
    
    /**
     * 根据ID查找轮播图
     * @param id 轮播图ID
     * @return 轮播图信息
     */
    Banner findById(@Param("id") Long id);
    
    /**
     * 查询所有轮播图（按排序顺序）
     * @return 轮播图列表
     */
    List<Banner> findAll();
    
    /**
     * 查询启用的轮播图（按排序顺序）
     * @return 启用的轮播图列表
     */
    List<Banner> findActivebanners();
    
    /**
     * 查询当前有效的轮播图（在时间范围内且启用）
     * @return 当前有效的轮播图列表
     */
    List<Banner> findCurrentValidBanners();
    
    /**
     * 根据创建者ID查询轮播图
     * @param createdBy 创建者ID
     * @return 轮播图列表
     */
    List<Banner> findByCreatedBy(@Param("createdBy") Long createdBy);
    
    /**
     * 更新轮播图信息
     * @param banner 轮播图信息
     * @return 影响行数
     */
    int update(Banner banner);
    
    /**
     * 删除轮播图
     * @param id 轮播图ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 批量删除轮播图
     * @param ids 轮播图ID列表
     * @return 影响行数
     */
    int deleteByIds(@Param("ids") List<Long> ids);
    
    /**
     * 更新轮播图状态
     * @param id 轮播图ID
     * @param isActive 是否启用
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("isActive") Boolean isActive);
    
    /**
     * 获取轮播图总数
     * @return 轮播图总数
     */
    long count();
    
    /**
     * 获取启用的轮播图总数
     * @return 启用的轮播图总数
     */
    long countActive();
}
