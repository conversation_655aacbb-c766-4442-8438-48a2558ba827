package com.lisong.controller;

import com.lisong.dto.BannerRequest;
import com.lisong.dto.BannerResponse;
import com.lisong.service.BannerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 轮播图控制器
 */
@RestController
@RequestMapping("/api/banners")
@CrossOrigin(origins = "*", maxAge = 3600)
public class BannerController {
    
    @Autowired
    private BannerService bannerService;
    
    /**
     * 创建轮播图
     * @param request 轮播图请求信息
     * @return 创建结果
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createBanner(@Valid @RequestBody BannerRequest request) {
        try {
            // 这里暂时使用固定的创建者ID，实际应该从认证信息中获取
            Long createdBy = 1L;
            BannerResponse response = bannerService.createBanner(request, createdBy);
            Map<String, Object> result = createSuccessResponse("创建轮播图成功", response);
            return ResponseEntity.status(HttpStatus.CREATED).body(result);
        } catch (IllegalArgumentException e) {
            Map<String, Object> result = createErrorResponse(e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
        } catch (Exception e) {
            Map<String, Object> result = createErrorResponse("创建轮播图失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 根据ID获取轮播图
     * @param id 轮播图ID
     * @return 轮播图信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getBannerById(@PathVariable Long id) {
        try {
            BannerResponse response = bannerService.getBannerById(id);
            Map<String, Object> result = createSuccessResponse("获取轮播图成功", response);
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            Map<String, Object> result = createErrorResponse(e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
        } catch (RuntimeException e) {
            Map<String, Object> result = createErrorResponse(e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        } catch (Exception e) {
            Map<String, Object> result = createErrorResponse("获取轮播图失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 获取所有轮播图
     * @return 轮播图列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllBanners() {
        try {
            List<BannerResponse> responses = bannerService.getAllBanners();
            Map<String, Object> result = createSuccessResponse("获取轮播图列表成功", responses);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> result = createErrorResponse("获取轮播图列表失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 获取启用的轮播图
     * @return 启用的轮播图列表
     */
    @GetMapping("/active")
    public ResponseEntity<Map<String, Object>> getActiveBanners() {
        try {
            List<BannerResponse> responses = bannerService.getActiveBanners();
            Map<String, Object> result = createSuccessResponse("获取启用轮播图列表成功", responses);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> result = createErrorResponse("获取启用轮播图列表失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 获取当前有效的轮播图
     * @return 当前有效的轮播图列表
     */
    @GetMapping("/current")
    public ResponseEntity<Map<String, Object>> getCurrentValidBanners() {
        try {
            List<BannerResponse> responses = bannerService.getCurrentValidBanners();
            Map<String, Object> result = createSuccessResponse("获取当前有效轮播图列表成功", responses);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> result = createErrorResponse("获取当前有效轮播图列表失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 根据创建者ID获取轮播图
     * @param createdBy 创建者ID
     * @return 轮播图列表
     */
    @GetMapping("/creator/{createdBy}")
    public ResponseEntity<Map<String, Object>> getBannersByCreatedBy(@PathVariable Long createdBy) {
        try {
            List<BannerResponse> responses = bannerService.getBannersByCreatedBy(createdBy);
            Map<String, Object> result = createSuccessResponse("获取创建者轮播图列表成功", responses);
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            Map<String, Object> result = createErrorResponse(e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
        } catch (Exception e) {
            Map<String, Object> result = createErrorResponse("获取创建者轮播图列表失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 更新轮播图
     * @param id 轮播图ID
     * @param request 轮播图请求信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateBanner(@PathVariable Long id, 
                                                           @Valid @RequestBody BannerRequest request) {
        try {
            BannerResponse response = bannerService.updateBanner(id, request);
            Map<String, Object> result = createSuccessResponse("更新轮播图成功", response);
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            Map<String, Object> result = createErrorResponse(e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
        } catch (RuntimeException e) {
            Map<String, Object> result = createErrorResponse(e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        } catch (Exception e) {
            Map<String, Object> result = createErrorResponse("更新轮播图失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 删除轮播图
     * @param id 轮播图ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteBanner(@PathVariable Long id) {
        try {
            boolean success = bannerService.deleteBanner(id);
            if (success) {
                Map<String, Object> result = createSuccessResponse("删除轮播图成功", null);
                return ResponseEntity.ok(result);
            } else {
                Map<String, Object> result = createErrorResponse("删除轮播图失败");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
            }
        } catch (IllegalArgumentException e) {
            Map<String, Object> result = createErrorResponse(e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
        } catch (RuntimeException e) {
            Map<String, Object> result = createErrorResponse(e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        } catch (Exception e) {
            Map<String, Object> result = createErrorResponse("删除轮播图失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 批量删除轮播图
     * @param ids 轮播图ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    public ResponseEntity<Map<String, Object>> deleteBanners(@RequestBody List<Long> ids) {
        try {
            int deletedCount = bannerService.deleteBanners(ids);
            Map<String, Object> result = createSuccessResponse("批量删除轮播图成功", 
                    Map.of("deletedCount", deletedCount));
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            Map<String, Object> result = createErrorResponse(e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
        } catch (Exception e) {
            Map<String, Object> result = createErrorResponse("批量删除轮播图失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 更新轮播图状态
     * @param id 轮播图ID
     * @param isActive 是否启用
     * @return 更新结果
     */
    @PatchMapping("/{id}/status")
    public ResponseEntity<Map<String, Object>> updateBannerStatus(@PathVariable Long id, 
                                                                 @RequestParam Boolean isActive) {
        try {
            boolean success = bannerService.updateBannerStatus(id, isActive);
            if (success) {
                Map<String, Object> result = createSuccessResponse("更新轮播图状态成功", null);
                return ResponseEntity.ok(result);
            } else {
                Map<String, Object> result = createErrorResponse("更新轮播图状态失败");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
            }
        } catch (IllegalArgumentException e) {
            Map<String, Object> result = createErrorResponse(e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
        } catch (RuntimeException e) {
            Map<String, Object> result = createErrorResponse(e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        } catch (Exception e) {
            Map<String, Object> result = createErrorResponse("更新轮播图状态失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 获取轮播图统计信息
     * @return 统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getBannerStats() {
        try {
            long totalCount = bannerService.getBannerCount();
            long activeCount = bannerService.getActiveBannerCount();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalCount", totalCount);
            stats.put("activeCount", activeCount);
            stats.put("inactiveCount", totalCount - activeCount);
            
            Map<String, Object> result = createSuccessResponse("获取轮播图统计信息成功", stats);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> result = createErrorResponse("获取轮播图统计信息失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("data", data);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
    
    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("data", null);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}
