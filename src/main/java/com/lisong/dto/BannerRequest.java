package com.lisong.dto;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 轮播图请求DTO
 */
@Data
public class BannerRequest {
    
    /**
     * 轮播图标题
     */
    @NotBlank(message = "轮播图标题不能为空")
    private String title;
    
    /**
     * 轮播图描述
     */
    private String description;
    
    /**
     * 轮播图图片URL
     */
    @NotBlank(message = "轮播图图片URL不能为空")
    private String imageUrl;
    
    /**
     * 点击跳转链接
     */
    private String linkUrl;
    
    /**
     * 排序顺序，数字越小越靠前
     */
    private Integer sortOrder = 0;
    
    /**
     * 是否启用
     */
    @NotNull(message = "是否启用不能为空")
    private Boolean isActive = true;
    
    /**
     * 开始显示时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /**
     * 结束显示时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
