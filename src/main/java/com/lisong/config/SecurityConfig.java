package com.lisong.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

/**
 * Spring Security配置
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(csrf -> csrf.disable())
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/h2-console/**").permitAll()
                .requestMatchers("/api/videos/file/**").permitAll() // 允许访问视频文件
                .requestMatchers("/api/videos/cover/**").permitAll() // 允许访问视频封面
                .requestMatchers("/api/videos").permitAll() // 允许查看视频列表
                .requestMatchers("/api/videos/{id}").permitAll() // 允许查看视频详情
                .requestMatchers("/api/videos/user/**").permitAll() // 允许查看用户视频
                .requestMatchers("/api/banners/**").permitAll() // 允许访问轮播图API
                .requestMatchers("/banner_management.html").permitAll() // 允许访问轮播图管理页面
                .requestMatchers("/api/admin/**").hasRole("ADMIN")
                .requestMatchers("/api/user/**").hasAnyRole("USER", "ADMIN")
                .requestMatchers("/api/videos/**").hasAnyRole("USER", "ADMIN") // 其他视频操作需要登录
                .anyRequest().authenticated()
            )
            .headers(headers -> headers.frameOptions().disable()); // 允许H2控制台使用iframe
            
        return http.build();
    }
}
