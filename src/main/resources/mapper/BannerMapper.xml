<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lisong.mapper.BannerMapper">

    <!-- 结果映射 -->
    <resultMap id="BannerResultMap" type="com.lisong.entity.Banner">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="description" column="description"/>
        <result property="imageUrl" column="image_url"/>
        <result property="linkUrl" column="link_url"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="isActive" column="is_active"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <!-- 关联创建者信息 -->
        <association property="creator" javaType="com.lisong.entity.User">
            <id property="id" column="creator_id"/>
            <result property="username" column="creator_username"/>
            <result property="nickname" column="creator_nickname"/>
        </association>
    </resultMap>

    <!-- 插入轮播图 -->
    <insert id="insert" parameterType="com.lisong.entity.Banner" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO banners (
            title, description, image_url, link_url, sort_order, 
            is_active, start_time, end_time, created_by
        ) VALUES (
            #{title}, #{description}, #{imageUrl}, #{linkUrl}, #{sortOrder},
            #{isActive}, #{startTime}, #{endTime}, #{createdBy}
        )
    </insert>

    <!-- 根据ID查找轮播图 -->
    <select id="findById" parameterType="long" resultMap="BannerResultMap">
        SELECT 
            b.id, b.title, b.description, b.image_url, b.link_url, 
            b.sort_order, b.is_active, b.start_time, b.end_time, 
            b.created_by, b.created_at, b.updated_at,
            u.id as creator_id, u.username as creator_username, u.nickname as creator_nickname
        FROM banners b
        LEFT JOIN users u ON b.created_by = u.id
        WHERE b.id = #{id}
    </select>

    <!-- 查询所有轮播图 -->
    <select id="findAll" resultMap="BannerResultMap">
        SELECT 
            b.id, b.title, b.description, b.image_url, b.link_url, 
            b.sort_order, b.is_active, b.start_time, b.end_time, 
            b.created_by, b.created_at, b.updated_at,
            u.id as creator_id, u.username as creator_username, u.nickname as creator_nickname
        FROM banners b
        LEFT JOIN users u ON b.created_by = u.id
        ORDER BY b.sort_order ASC, b.created_at DESC
    </select>

    <!-- 查询启用的轮播图 -->
    <select id="findActivebanners" resultMap="BannerResultMap">
        SELECT 
            b.id, b.title, b.description, b.image_url, b.link_url, 
            b.sort_order, b.is_active, b.start_time, b.end_time, 
            b.created_by, b.created_at, b.updated_at,
            u.id as creator_id, u.username as creator_username, u.nickname as creator_nickname
        FROM banners b
        LEFT JOIN users u ON b.created_by = u.id
        WHERE b.is_active = true
        ORDER BY b.sort_order ASC, b.created_at DESC
    </select>

    <!-- 查询当前有效的轮播图 -->
    <select id="findCurrentValidBanners" resultMap="BannerResultMap">
        SELECT 
            b.id, b.title, b.description, b.image_url, b.link_url, 
            b.sort_order, b.is_active, b.start_time, b.end_time, 
            b.created_by, b.created_at, b.updated_at,
            u.id as creator_id, u.username as creator_username, u.nickname as creator_nickname
        FROM banners b
        LEFT JOIN users u ON b.created_by = u.id
        WHERE b.is_active = true
        AND (b.start_time IS NULL OR b.start_time &lt;= NOW())
        AND (b.end_time IS NULL OR b.end_time &gt;= NOW())
        ORDER BY b.sort_order ASC, b.created_at DESC
    </select>

    <!-- 根据创建者ID查询轮播图 -->
    <select id="findByCreatedBy" parameterType="long" resultMap="BannerResultMap">
        SELECT 
            b.id, b.title, b.description, b.image_url, b.link_url, 
            b.sort_order, b.is_active, b.start_time, b.end_time, 
            b.created_by, b.created_at, b.updated_at,
            u.id as creator_id, u.username as creator_username, u.nickname as creator_nickname
        FROM banners b
        LEFT JOIN users u ON b.created_by = u.id
        WHERE b.created_by = #{createdBy}
        ORDER BY b.sort_order ASC, b.created_at DESC
    </select>

    <!-- 更新轮播图 -->
    <update id="update" parameterType="com.lisong.entity.Banner">
        UPDATE banners SET
            title = #{title},
            description = #{description},
            image_url = #{imageUrl},
            link_url = #{linkUrl},
            sort_order = #{sortOrder},
            is_active = #{isActive},
            start_time = #{startTime},
            end_time = #{endTime},
            updated_at = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <!-- 删除轮播图 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM banners WHERE id = #{id}
    </delete>

    <!-- 批量删除轮播图 -->
    <delete id="deleteByIds">
        DELETE FROM banners WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 更新轮播图状态 -->
    <update id="updateStatus">
        UPDATE banners SET 
            is_active = #{isActive},
            updated_at = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <!-- 获取轮播图总数 -->
    <select id="count" resultType="long">
        SELECT COUNT(*) FROM banners
    </select>

    <!-- 获取启用的轮播图总数 -->
    <select id="countActive" resultType="long">
        SELECT COUNT(*) FROM banners WHERE is_active = true
    </select>

</mapper>
