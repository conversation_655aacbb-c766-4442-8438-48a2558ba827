# 轮播图管理系统

这是一个基于Spring Boot的轮播图增删改查系统，采用四层架构设计，支持完整的CRUD操作。

## 功能特性

### 核心功能
- ✅ **轮播图增删改查**：完整的CRUD操作
- ✅ **状态管理**：支持启用/禁用轮播图
- ✅ **时间控制**：支持设置轮播图的显示时间范围
- ✅ **排序功能**：支持自定义轮播图显示顺序
- ✅ **批量操作**：支持批量删除轮播图
- ✅ **统计信息**：显示轮播图总数、启用数量等统计数据

### 高级功能
- 🔍 **多种查询方式**：
  - 查询所有轮播图
  - 查询启用的轮播图
  - 查询当前有效的轮播图（在时间范围内且启用）
  - 根据创建者查询轮播图
- 📊 **实时统计**：总数量、启用数量、禁用数量
- 🎨 **响应式界面**：美观的Web管理界面
- 🔒 **数据验证**：完整的前后端数据验证

## 技术架构

### 后端技术栈
- **Spring Boot 3.4.6**：主框架
- **MyBatis**：数据持久化
- **H2 Database**：内存数据库
- **Spring Validation**：数据验证
- **Lombok**：简化代码

### 前端技术栈
- **HTML5 + CSS3**：页面结构和样式
- **JavaScript (ES6+)**：交互逻辑
- **Fetch API**：HTTP请求
- **响应式设计**：适配不同屏幕尺寸

## 项目结构

```
src/main/java/com/lisong/
├── entity/
│   └── Banner.java                  # 轮播图实体类
├── dto/
│   ├── BannerRequest.java          # 轮播图请求DTO
│   └── BannerResponse.java         # 轮播图响应DTO
├── mapper/
│   └── BannerMapper.java           # 轮播图数据访问接口
├── service/
│   ├── BannerService.java          # 轮播图服务接口
│   └── impl/
│       └── BannerServiceImpl.java  # 轮播图服务实现
└── controller/
    └── BannerController.java       # 轮播图控制器

src/main/resources/
├── mapper/
│   └── BannerMapper.xml            # MyBatis映射文件
├── schema.sql                      # 数据库表结构
└── data.sql                        # 初始化数据

banner_management.html              # 轮播图管理界面
```

## 数据库设计

### 轮播图表 (banners)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键，自增 |
| title | VARCHAR(255) | 轮播图标题 |
| description | TEXT | 轮播图描述 |
| image_url | VARCHAR(500) | 图片URL |
| link_url | VARCHAR(500) | 点击跳转链接 |
| sort_order | INTEGER | 排序顺序 |
| is_active | BOOLEAN | 是否启用 |
| start_time | TIMESTAMP | 开始显示时间 |
| end_time | TIMESTAMP | 结束显示时间 |
| created_by | BIGINT | 创建者ID |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## API接口文档

### 基础URL
```
http://localhost:8080/api/banners
```

### 接口列表

#### 1. 创建轮播图
- **POST** `/api/banners`
- **请求体**：
```json
{
  "title": "轮播图标题",
  "description": "轮播图描述",
  "imageUrl": "https://example.com/image.jpg",
  "linkUrl": "https://example.com/link",
  "sortOrder": 0,
  "isActive": true,
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}
```

#### 2. 获取轮播图列表
- **GET** `/api/banners` - 获取所有轮播图
- **GET** `/api/banners/active` - 获取启用的轮播图
- **GET** `/api/banners/current` - 获取当前有效的轮播图

#### 3. 获取单个轮播图
- **GET** `/api/banners/{id}`

#### 4. 更新轮播图
- **PUT** `/api/banners/{id}`

#### 5. 删除轮播图
- **DELETE** `/api/banners/{id}` - 删除单个
- **DELETE** `/api/banners/batch` - 批量删除

#### 6. 更新状态
- **PATCH** `/api/banners/{id}/status?isActive=true`

#### 7. 获取统计信息
- **GET** `/api/banners/stats`

### 响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": { ... },
  "timestamp": 1640995200000
}
```

## 快速开始

### 1. 启动应用
```bash
mvn spring-boot:run
```

### 2. 访问管理界面
打开浏览器访问：`http://localhost:8080/banner_management.html`

### 3. 测试API
使用Postman或其他API测试工具测试接口：
```bash
# 获取所有轮播图
curl -X GET http://localhost:8080/api/banners

# 创建轮播图
curl -X POST http://localhost:8080/api/banners \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试轮播图",
    "description": "这是一个测试轮播图",
    "imageUrl": "https://example.com/test.jpg",
    "isActive": true
  }'
```

## 使用说明

### 管理界面功能

1. **统计面板**：显示轮播图总数、启用数量、禁用数量
2. **操作按钮**：
   - 新建轮播图：创建新的轮播图
   - 刷新列表：重新加载轮播图列表
   - 仅显示启用：只显示启用状态的轮播图
   - 当前有效：显示当前时间范围内有效的轮播图

3. **轮播图列表**：
   - 显示轮播图缩略图、标题、描述等信息
   - 支持编辑、启用/禁用、删除操作
   - 显示创建时间、创建者等元数据

4. **编辑表单**：
   - 标题和描述输入
   - 图片URL和跳转链接设置
   - 排序顺序和状态控制
   - 显示时间范围设置

### 最佳实践

1. **图片建议**：
   - 推荐尺寸：800x400像素
   - 支持格式：JPG、PNG、WebP
   - 建议使用CDN或图床服务

2. **排序规则**：
   - 数字越小越靠前显示
   - 相同排序按创建时间倒序

3. **时间控制**：
   - 不设置时间表示永久有效
   - 开始时间不能晚于结束时间
   - 系统会自动过滤过期的轮播图

## 扩展功能

### 可扩展的功能点
- 🔄 **图片上传**：集成文件上传功能
- 👥 **权限控制**：基于角色的访问控制
- 📱 **移动端适配**：响应式设计优化
- 📈 **点击统计**：轮播图点击量统计
- 🎯 **A/B测试**：支持轮播图效果测试
- 🔔 **消息通知**：轮播图状态变更通知

## 故障排除

### 常见问题

1. **图片不显示**：
   - 检查图片URL是否有效
   - 确认图片服务器支持跨域访问

2. **时间格式错误**：
   - 使用标准格式：`yyyy-MM-dd HH:mm:ss`
   - 确保开始时间早于结束时间

3. **API调用失败**：
   - 检查服务是否正常启动
   - 确认请求格式和参数正确

### 日志查看
```bash
# 查看应用日志
tail -f logs/application.log

# 查看数据库操作日志
# 在application.properties中启用SQL日志
logging.level.com.lisong.mapper=DEBUG
```

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发环境要求
- JDK 17+
- Maven 3.6+
- IDE（推荐IntelliJ IDEA）

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 添加必要的注释和文档

---

**作者**：李松  
**版本**：1.0.0  
**更新时间**：2024年1月
