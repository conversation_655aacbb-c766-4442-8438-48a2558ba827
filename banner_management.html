<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            flex: 1;
            margin: 0 10px;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #5a6fd8;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .banner-list {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .banner-item {
            display: flex;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
        }

        .banner-item:hover {
            background-color: #f8f9fa;
        }

        .banner-item:last-child {
            border-bottom: none;
        }

        .banner-image {
            width: 120px;
            height: 80px;
            object-fit: cover;
            border-radius: 5px;
            margin-right: 20px;
        }

        .banner-info {
            flex: 1;
        }

        .banner-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .banner-description {
            color: #666;
            margin-bottom: 10px;
        }

        .banner-meta {
            font-size: 12px;
            color: #999;
        }

        .banner-actions {
            display: flex;
            gap: 10px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .message {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            display: none;
        }

        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state img {
            width: 100px;
            opacity: 0.5;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>轮播图管理系统</h1>
            <p>管理网站轮播图，支持增删改查操作</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalCount">0</div>
                <div>总轮播图</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeCount">0</div>
                <div>启用中</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="inactiveCount">0</div>
                <div>已禁用</div>
            </div>
        </div>

        <!-- 操作控制 -->
        <div class="controls">
            <button class="btn btn-success" onclick="showCreateModal()">
                ➕ 新建轮播图
            </button>
            <button class="btn" onclick="loadAllBanners()">
                🔄 刷新列表
            </button>
            <button class="btn" onclick="loadActiveBanners()">
                ✅ 仅显示启用
            </button>
            <button class="btn" onclick="loadCurrentValidBanners()">
                ⏰ 当前有效
            </button>
        </div>

        <!-- 消息提示 -->
        <div id="message" class="message"></div>

        <!-- 轮播图列表 -->
        <div class="banner-list">
            <div id="bannerContainer">
                <div class="loading">正在加载轮播图列表...</div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑轮播图模态框 -->
    <div id="bannerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">新建轮播图</h2>
                <span class="close" onclick="closeBannerModal()">&times;</span>
            </div>
            <form id="bannerForm">
                <div class="form-group">
                    <label for="title">标题 *</label>
                    <input type="text" id="title" name="title" required>
                </div>
                
                <div class="form-group">
                    <label for="description">描述</label>
                    <textarea id="description" name="description" placeholder="轮播图描述信息"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="imageUrl">图片URL *</label>
                    <input type="url" id="imageUrl" name="imageUrl" required 
                           placeholder="https://example.com/image.jpg">
                </div>
                
                <div class="form-group">
                    <label for="linkUrl">跳转链接</label>
                    <input type="url" id="linkUrl" name="linkUrl" 
                           placeholder="https://example.com">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="sortOrder">排序顺序</label>
                        <input type="number" id="sortOrder" name="sortOrder" value="0" min="0">
                    </div>
                    <div class="form-group">
                        <label for="isActive">状态</label>
                        <select id="isActive" name="isActive">
                            <option value="true">启用</option>
                            <option value="false">禁用</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="startTime">开始时间</label>
                        <input type="datetime-local" id="startTime" name="startTime">
                    </div>
                    <div class="form-group">
                        <label for="endTime">结束时间</label>
                        <input type="datetime-local" id="endTime" name="endTime">
                    </div>
                </div>
                
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn" onclick="closeBannerModal()">取消</button>
                    <button type="submit" class="btn btn-success" id="submitBtn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api/banners';
        let currentEditId = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadAllBanners();
        });

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`);
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('totalCount').textContent = result.data.totalCount;
                    document.getElementById('activeCount').textContent = result.data.activeCount;
                    document.getElementById('inactiveCount').textContent = result.data.inactiveCount;
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 加载所有轮播图
        async function loadAllBanners() {
            await loadBanners(`${API_BASE}`);
        }

        // 加载启用的轮播图
        async function loadActiveBanners() {
            await loadBanners(`${API_BASE}/active`);
        }

        // 加载当前有效的轮播图
        async function loadCurrentValidBanners() {
            await loadBanners(`${API_BASE}/current`);
        }

        // 通用加载轮播图方法
        async function loadBanners(url) {
            const container = document.getElementById('bannerContainer');
            container.innerHTML = '<div class="loading">正在加载轮播图列表...</div>';

            try {
                const response = await fetch(url);
                const result = await response.json();

                if (result.success) {
                    displayBanners(result.data);
                } else {
                    showMessage(result.message, 'error');
                    container.innerHTML = '<div class="empty-state">加载失败</div>';
                }
            } catch (error) {
                console.error('加载轮播图失败:', error);
                showMessage('加载轮播图失败: ' + error.message, 'error');
                container.innerHTML = '<div class="empty-state">加载失败</div>';
            }
        }

        // 显示轮播图列表
        function displayBanners(banners) {
            const container = document.getElementById('bannerContainer');
            
            if (!banners || banners.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <p>暂无轮播图数据</p>
                        <button class="btn btn-success" onclick="showCreateModal()">创建第一个轮播图</button>
                    </div>
                `;
                return;
            }

            container.innerHTML = banners.map(banner => `
                <div class="banner-item">
                    <img src="${banner.imageUrl}" alt="${banner.title}" class="banner-image" 
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjgwIiB2aWV3Qm94PSIwIDAgMTIwIDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTIwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00MCA0MEw4MCA0MEw2MCA2MEw0MCA0MFoiIGZpbGw9IiNEREREREQiLz4KPC9zdmc+'">
                    <div class="banner-info">
                        <div class="banner-title">${banner.title}</div>
                        <div class="banner-description">${banner.description || '暂无描述'}</div>
                        <div class="banner-meta">
                            <span class="status-badge ${banner.isActive ? 'status-active' : 'status-inactive'}">
                                ${banner.isActive ? '启用' : '禁用'}
                            </span>
                            排序: ${banner.sortOrder} | 
                            创建时间: ${formatDateTime(banner.createdAt)} |
                            创建者: ${banner.creatorUsername || '未知'}
                            ${banner.startTime ? `| 开始: ${formatDateTime(banner.startTime)}` : ''}
                            ${banner.endTime ? `| 结束: ${formatDateTime(banner.endTime)}` : ''}
                        </div>
                    </div>
                    <div class="banner-actions">
                        <button class="btn btn-warning" onclick="editBanner(${banner.id})">编辑</button>
                        <button class="btn ${banner.isActive ? 'btn-warning' : 'btn-success'}" 
                                onclick="toggleBannerStatus(${banner.id}, ${!banner.isActive})">
                            ${banner.isActive ? '禁用' : '启用'}
                        </button>
                        <button class="btn btn-danger" onclick="deleteBanner(${banner.id})">删除</button>
                    </div>
                </div>
            `).join('');
        }

        // 显示创建模态框
        function showCreateModal() {
            currentEditId = null;
            document.getElementById('modalTitle').textContent = '新建轮播图';
            document.getElementById('submitBtn').textContent = '创建';
            document.getElementById('bannerForm').reset();
            document.getElementById('bannerModal').style.display = 'block';
        }

        // 编辑轮播图
        async function editBanner(id) {
            try {
                const response = await fetch(`${API_BASE}/${id}`);
                const result = await response.json();

                if (result.success) {
                    const banner = result.data;
                    currentEditId = id;
                    
                    document.getElementById('modalTitle').textContent = '编辑轮播图';
                    document.getElementById('submitBtn').textContent = '更新';
                    
                    // 填充表单
                    document.getElementById('title').value = banner.title;
                    document.getElementById('description').value = banner.description || '';
                    document.getElementById('imageUrl').value = banner.imageUrl;
                    document.getElementById('linkUrl').value = banner.linkUrl || '';
                    document.getElementById('sortOrder').value = banner.sortOrder;
                    document.getElementById('isActive').value = banner.isActive.toString();
                    
                    if (banner.startTime) {
                        document.getElementById('startTime').value = formatDateTimeForInput(banner.startTime);
                    }
                    if (banner.endTime) {
                        document.getElementById('endTime').value = formatDateTimeForInput(banner.endTime);
                    }
                    
                    document.getElementById('bannerModal').style.display = 'block';
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                console.error('获取轮播图详情失败:', error);
                showMessage('获取轮播图详情失败: ' + error.message, 'error');
            }
        }

        // 关闭模态框
        function closeBannerModal() {
            document.getElementById('bannerModal').style.display = 'none';
            currentEditId = null;
        }

        // 表单提交
        document.getElementById('bannerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const bannerData = {
                title: formData.get('title'),
                description: formData.get('description'),
                imageUrl: formData.get('imageUrl'),
                linkUrl: formData.get('linkUrl'),
                sortOrder: parseInt(formData.get('sortOrder')) || 0,
                isActive: formData.get('isActive') === 'true',
                startTime: formData.get('startTime') || null,
                endTime: formData.get('endTime') || null
            };

            try {
                const url = currentEditId ? `${API_BASE}/${currentEditId}` : API_BASE;
                const method = currentEditId ? 'PUT' : 'POST';
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(bannerData)
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(result.message, 'success');
                    closeBannerModal();
                    loadAllBanners();
                    loadStats();
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                console.error('保存轮播图失败:', error);
                showMessage('保存轮播图失败: ' + error.message, 'error');
            }
        });

        // 切换轮播图状态
        async function toggleBannerStatus(id, isActive) {
            try {
                const response = await fetch(`${API_BASE}/${id}/status?isActive=${isActive}`, {
                    method: 'PATCH'
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(result.message, 'success');
                    loadAllBanners();
                    loadStats();
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                console.error('更新轮播图状态失败:', error);
                showMessage('更新轮播图状态失败: ' + error.message, 'error');
            }
        }

        // 删除轮播图
        async function deleteBanner(id) {
            if (!confirm('确定要删除这个轮播图吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(result.message, 'success');
                    loadAllBanners();
                    loadStats();
                } else {
                    showMessage(result.message, 'error');
                }
            } catch (error) {
                console.error('删除轮播图失败:', error);
                showMessage('删除轮播图失败: ' + error.message, 'error');
            }
        }

        // 显示消息
        function showMessage(message, type) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = message;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';
            
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 5000);
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        }

        // 格式化日期时间为输入框格式
        function formatDateTimeForInput(dateTimeStr) {
            if (!dateTimeStr) return '';
            const date = new Date(dateTimeStr);
            return date.toISOString().slice(0, 16);
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('bannerModal');
            if (event.target === modal) {
                closeBannerModal();
            }
        }
    </script>
</body>
</html>
