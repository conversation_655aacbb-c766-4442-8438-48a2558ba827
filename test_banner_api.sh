#!/bin/bash

# 轮播图API测试脚本
BASE_URL="http://localhost:8080/api/banners"

echo "=== 轮播图API测试脚本 ==="
echo

# 1. 获取统计信息
echo "1. 获取统计信息:"
curl -s "$BASE_URL/stats" | jq '.'
echo
echo

# 2. 获取所有轮播图
echo "2. 获取所有轮播图:"
curl -s "$BASE_URL" | jq '.data | length'
echo "轮播图数量: $(curl -s "$BASE_URL" | jq '.data | length')"
echo

# 3. 获取启用的轮播图
echo "3. 获取启用的轮播图:"
curl -s "$BASE_URL/active" | jq '.data | length'
echo "启用轮播图数量: $(curl -s "$BASE_URL/active" | jq '.data | length')"
echo

# 4. 获取当前有效的轮播图
echo "4. 获取当前有效的轮播图:"
curl -s "$BASE_URL/current" | jq '.data | length'
echo "当前有效轮播图数量: $(curl -s "$BASE_URL/current" | jq '.data | length')"
echo

# 5. 创建新轮播图
echo "5. 创建新轮播图:"
CREATE_RESPONSE=$(curl -s -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "API测试轮播图",
    "description": "通过测试脚本创建的轮播图",
    "imageUrl": "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop",
    "linkUrl": "https://example.com/api-test",
    "sortOrder": 10,
    "isActive": true
  }')

echo "$CREATE_RESPONSE" | jq '.'
BANNER_ID=$(echo "$CREATE_RESPONSE" | jq -r '.data.id')
echo "创建的轮播图ID: $BANNER_ID"
echo

# 6. 获取单个轮播图
echo "6. 获取单个轮播图 (ID: $BANNER_ID):"
curl -s "$BASE_URL/$BANNER_ID" | jq '.data.title'
echo

# 7. 更新轮播图
echo "7. 更新轮播图 (ID: $BANNER_ID):"
UPDATE_RESPONSE=$(curl -s -X PUT "$BASE_URL/$BANNER_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "更新后的API测试轮播图",
    "description": "这个轮播图已经被更新了",
    "imageUrl": "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop",
    "linkUrl": "https://example.com/api-test-updated",
    "sortOrder": 5,
    "isActive": true
  }')

echo "$UPDATE_RESPONSE" | jq '.message'
echo

# 8. 更新轮播图状态
echo "8. 禁用轮播图 (ID: $BANNER_ID):"
STATUS_RESPONSE=$(curl -s -X PATCH "$BASE_URL/$BANNER_ID/status?isActive=false")
echo "$STATUS_RESPONSE" | jq '.message'
echo

# 9. 验证状态更新
echo "9. 验证状态更新:"
curl -s "$BASE_URL/$BANNER_ID" | jq '.data.isActive'
echo

# 10. 重新启用轮播图
echo "10. 重新启用轮播图 (ID: $BANNER_ID):"
STATUS_RESPONSE=$(curl -s -X PATCH "$BASE_URL/$BANNER_ID/status?isActive=true")
echo "$STATUS_RESPONSE" | jq '.message'
echo

# 11. 删除轮播图
echo "11. 删除轮播图 (ID: $BANNER_ID):"
DELETE_RESPONSE=$(curl -s -X DELETE "$BASE_URL/$BANNER_ID")
echo "$DELETE_RESPONSE" | jq '.message'
echo

# 12. 验证删除
echo "12. 验证删除 (应该返回404):"
curl -s "$BASE_URL/$BANNER_ID" | jq '.success'
echo

# 13. 最终统计
echo "13. 最终统计信息:"
curl -s "$BASE_URL/stats" | jq '.'
echo

echo "=== 测试完成 ==="
